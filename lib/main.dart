import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/bloc/cubit/absence_cubit/absence_cubit.dart';
import 'package:busaty_parents/bloc/cubit/add_son_cubit/add_son_cubit.dart';
import 'package:busaty_parents/bloc/cubit/address_change_cubit/address_change_cubit.dart';
import 'package:busaty_parents/bloc/cubit/ads_cubit/ads_cubit.dart';
import 'package:busaty_parents/bloc/cubit/carousel_cubit/carousel_cubit.dart';
import 'package:busaty_parents/bloc/cubit/temporary_address_change_cubit/temporary_address_change_cubit.dart';
import 'package:busaty_parents/bloc/cubit/temporary_address_requests_cubit/temporary_address_requests_cubit.dart';
import 'package:busaty_parents/bloc/cubit/change_password_cubit/change_password_cubit.dart';
import 'package:busaty_parents/bloc/cubit/check_internet_cubit/check_internet_cubit.dart';
import 'package:busaty_parents/bloc/cubit/children_cubit/children_cubit.dart';
import 'package:busaty_parents/bloc/cubit/children_onmap_cubit/child_on_map_cubit.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_cubit.dart';
import 'package:busaty_parents/bloc/cubit/forgot_password_cubit/forgot_password_cubit.dart';
import 'package:busaty_parents/bloc/cubit/login_cubit/login_cubit.dart';
import 'package:busaty_parents/bloc/cubit/logout_cubit/logout_cubit.dart';
import 'package:busaty_parents/bloc/cubit/notifications_cubit/notifications_cubit.dart';
import 'package:busaty_parents/bloc/cubit/profile_cubit/profile_cubit.dart';
import 'package:busaty_parents/bloc/cubit/register_cubit/register_cubit.dart';
import 'package:busaty_parents/bloc/cubit/son_by_id_cubit/son_by_id_cubit.dart';
import 'package:busaty_parents/bloc/cubit/subscription_cubit/subscription_cubit.dart';
import 'package:busaty_parents/bloc/cubit/upload_profile_image_cubit/upload_profile_image_cubit.dart';
import 'package:busaty_parents/bloc/cubit/complete_profile_cubit/complete_profile_cubit.dart';
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/route.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/notification_service/notification_manager.dart';
import 'package:busaty_parents/notification_service/background_handler.dart';
import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/services/payment_service.dart';
import 'package:busaty_parents/translations/codegen_loader.g.dart';
import 'package:busaty_parents/utils/get_it_injection.dart';
import 'package:busaty_parents/utils/app_startup_helper.dart';
import 'package:busaty_parents/services/app_update_service.dart';
import 'package:busaty_parents/config/app_update_config.dart';
import 'package:busaty_parents/views/screens/home_screen/home_screen.dart';
import 'package:busaty_parents/views/screens/languages_screen/on_boarding_language_screen.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:timezone/data/latest.dart' as tz;

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize Firebase Core first
    await Firebase.initializeApp();
    Logger.i('Firebase Core initialized');
    await CacheHelper.init();

    // Register background message handler ONCE at the top level
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    Logger.i('Background message handler registered');

    // CRITICAL: Set foreground notification presentation options ONCE here
    // This prevents the system from showing automatic notifications
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: false, // We handle notifications manually
      badge: false, // We handle badges manually
      sound: false, // We handle sounds manually
    );

    Logger.i(
        'Foreground notification presentation disabled - manual handling only');

    // Initialize timezone
    tz.initializeTimeZones();
    Logger.i('Timezone initialized');

    // Initialize other services that don't depend on Firebase Messaging
    await EasyLocalization.ensureInitialized();
    await PaymentService.instance.initConnection();
    await init();
    token = CacheHelper.getString("token") ?? '';

    // Initialize authentication method from cache
    authMethod = CacheHelper.getString(authMethodKey);

    // Log the authentication method during app initialization
    Logger.i("App Initialization - Authentication Method Check");
    Logger.d("Auth method from cache during initialization: $authMethod");
    Logger.d("Is Google auth: ${authMethod == authMethodGoogle}");
    Logger.d("Is Regular auth: ${authMethod == authMethodRegular}");

    // Force debug output to console
    if (authMethod == authMethodGoogle) {
      Logger.i("USER IS AUTHENTICATED WITH GOOGLE");
    } else if (authMethod == authMethodRegular) {
      Logger.i("USER IS AUTHENTICATED WITH REGULAR EMAIL/PASSWORD");
    } else {
      Logger.i("USER AUTHENTICATION METHOD UNKNOWN OR NOT SET");
    }

    // Run the app first
    runApp(EasyLocalization(
      supportedLocales: [const Locale('en'), const Locale('ar')],
      path: 'assets/translations',
      assetLoader: const CodegenLoader(),
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => LoginCubit(),
          ),
          BlocProvider(
            create: (context) => RegisterCubit(),
          ),
          BlocProvider(
            create: (context) => ChildrenCubit(),
          ),
          BlocProvider(
            create: (context) => ChildOnMapCubit(),
          ),
          BlocProvider(
            create: (context) => AbsenceCubit(),
          ),
          BlocProvider(
            create: (context) => AddressChangeCubit(),
          ),
          BlocProvider(
            create: (context) => TemporaryAddressChangeCubit(),
          ),
          BlocProvider(
            create: (context) => TemporaryAddressRequestsCubit(),
          ),
          BlocProvider(
            create: (context) => LogoutCubit(),
          ),
          BlocProvider(
            create: (context) => NotificationsCubit(),
          ),
          BlocProvider(
            create: (context) => ProfileCubit()..getProfile(),
          ),
          BlocProvider(
            create: (context) => ForgotPasswordCubit(),
          ),
          BlocProvider(
            create: (context) => SubscriptionCubit(),
          ),
          BlocProvider(
            create: (context) => CarouselCubit(),
          ),
          BlocProvider(
            create: (context) => AdsCubit(),
          ),
          BlocProvider(
            create: (context) => ChangePasswordCubit(),
          ),
          BlocProvider(
            create: (context) => CurrentTripCubit()..getCurrentTrip(),
          ),
          BlocProvider(
            create: (context) => UploadProfileImageCubit(),
          ),
          BlocProvider(
            create: (context) => SonByIdCubit(),
          ),
          BlocProvider(
            create: (context) => AddSonCubit(),
          ),
          BlocProvider(
            create: (context) => CheckInternetCubit(),
          ),
          BlocProvider(
            create: (context) => CompleteProfileCubit(),
          ),
        ],
        child: const MyApp(),
      ),
    ));

    // Initialize the new notification system
    try {
      Logger.i('Starting notification system initialization...');

      // Initialize the complete notification system
      await NotificationManager.initialize();

      // Get FCM token
      final fcmToken = await NotificationManager.getFCMToken();
      if (fcmToken != null) {
        Logger.i('FCM token obtained during initialization');
        initializeFCMToken();
      } else {
        Logger.w('Failed to obtain FCM token during initialization');
        // Retry token acquisition in background
        Future.delayed(const Duration(seconds: 5), () async {
          final retryToken = await NotificationManager.getFCMToken();
          if (retryToken != null) {
            Logger.i('FCM token obtained on retry');
            initializeFCMToken();
          }
        });
      }

      Logger.i('Notification system initialization completed successfully');
    } catch (e, stack) {
      Logger.e('Failed to initialize notification system', e, stack);
      // Don't rethrow - app should continue even if notifications fail
    }
  } catch (e, stack) {
    Logger.e('Error initializing app', e, stack);
    rethrow;
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Widget? _appWidget;
  bool _isCheckingMaintenance = true;

  @override
  void initState() {
    super.initState();
    _checkMaintenanceStatus();
  }

  Future<void> _checkMaintenanceStatus() async {
    try {
      final normalApp = _buildNormalApp();

      final checkedWidget = await AppStartupHelper.checkAppStatusAndGetWidget(
        appName: AppUpdateConfig.defaultAppName,
        appType: AppType.parents,
        normalApp: normalApp,
        onRetry: _checkMaintenanceStatus,
      );

      if (mounted) {
        setState(() {
          _appWidget = checkedWidget;
          _isCheckingMaintenance = false;
        });
      }
    } catch (e) {
      // On error, show normal app
      if (mounted) {
        setState(() {
          _appWidget = _buildNormalApp();
          _isCheckingMaintenance = false;
        });
      }
    }
  }

  Widget _buildNormalApp() {
    return ScreenUtilInit(
      designSize: const Size(428, 926),
      builder: (context, state) {
        return MaterialApp(
            title: 'Busaty - Parents'.tr(),
            debugShowCheckedModeBanner: false,
            scaffoldMessengerKey: snackBarKey,
            navigatorKey: navigatorKey,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            theme: ThemeData(
              primarySwatch: Colors.blue,
            ),
            routes: routes,
            home: token == null || token == ''
                ? const OnBoardingLanguageScreen()
                : const HomeScreen());
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingMaintenance) {
      return MaterialApp(
        title: 'Busaty - Parents',
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'checking'.tr(),
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return _appWidget ?? _buildNormalApp();
  }
}



//Client ID: 545165014521-bivi1r2dg3ljt7k7cv1v4lv6nrgtl5dd
//├── SHA-1 للـ Debug: 8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23
//├── SHA-1 للـ Release: 37:E7:6F:71:ED:2B:A3:72:02:29:92:BD:D2:B3:47:8E:DF:D5:DF:E7
//└── SHA-1 للـ Play Store: 0A:D0:64:D1:9D:60:62:07:43:6C:53:01:03:07:11:6F:59:FA:2C:69
//com.busaty.parent